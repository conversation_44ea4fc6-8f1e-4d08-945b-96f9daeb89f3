2025-07-29 13:54:01.446 [main] WARN  org.apache.catalina.loader.WebappClassLoaderBase - The web application [ROOT] appears to have started a thread named [lettuce-timer-3-1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.lang.Thread.sleep(Native Method)
 io.netty.util.HashedWheelTimer$Worker.waitForNextTick(HashedWheelTimer.java:600)
 io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:496)
 io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
 java.lang.Thread.run(Thread.java:750)
2025-07-29 13:54:01.451 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
2025-07-29 14:07:33.708 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.dto.StatisticsDto ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-29 14:07:33.814 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResourceLink ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-29 14:07:33.827 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResource ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-29 14:07:33.989 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.SytSysOrganizationUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-29 14:07:35.551 [main] WARN  com.sanyth.auth.server.filter.MyFilterSecurityInterceptor - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-07-29 14:07:37.392 [main] WARN  org.springframework.security.config.annotation.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='classpath:/static']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-29 14:07:38.131 [main] WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-29 14:07:41.043 [main] WARN  com.zaxxer.hikari.HikariConfig - DatebookHikariCP - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-29 14:07:41.056 [main] WARN  com.zaxxer.hikari.util.DriverDataSource - Registered driver with driverClassName=oracle.jdbc.driver.OracleDriver was not found, trying direct instantiation.
2025-07-29 14:15:42.497 [DatebookHikariCP housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - DatebookHikariCP - Retrograde clock change detected (housekeeper delta=29s776ms), soft-evicting connections from pool.
