2025-07-29 13:53:57.120 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-07-29 13:53:57.381 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Starting AuthServerApplication using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 2130 (/Users/<USER>/WorkSpace/git-sanyth-sso/git-sanyth-sso-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/git-sanyth-sso)
2025-07-29 13:53:57.381 [main] INFO  com.sanyth.auth.server.AuthServerApplication - The following 1 profile is active: "loc"
2025-07-29 13:53:58.813 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 13:53:58.816 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-07-29 13:53:58.948 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 110 ms. Found 2 LDAP repository interfaces.
2025-07-29 13:53:58.973 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 13:53:58.974 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-07-29 13:53:58.984 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-29 13:53:58.985 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-29 13:53:58.985 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 MongoDB repository interfaces.
2025-07-29 13:53:59.000 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 13:53:59.002 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-29 13:53:59.017 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-29 13:53:59.018 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-29 13:53:59.018 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-07-29 13:53:59.692 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 13:53:59.707 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 13:53:59.732 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 13:54:00.196 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8090 (http)
2025-07-29 13:54:00.220 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8090"]
2025-07-29 13:54:00.221 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 13:54:00.221 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-29 13:54:00.351 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-29 13:54:00.352 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2846 ms
2025-07-29 13:54:01.024 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-29 13:54:01.025 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-29 13:54:01.025 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-29 13:54:01.425 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-29 13:54:01.493 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-29 14:07:27.949 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-07-29 14:07:28.221 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Starting AuthServerApplication using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 3506 (/Users/<USER>/WorkSpace/git-sanyth-sso/git-sanyth-sso-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/git-sanyth-sso)
2025-07-29 14:07:28.222 [main] INFO  com.sanyth.auth.server.AuthServerApplication - The following 1 profile is active: "loc"
2025-07-29 14:07:30.175 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 14:07:30.179 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-07-29 14:07:30.322 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 115 ms. Found 2 LDAP repository interfaces.
2025-07-29 14:07:30.347 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 14:07:30.348 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-07-29 14:07:30.357 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-29 14:07:30.357 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-29 14:07:30.358 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 MongoDB repository interfaces.
2025-07-29 14:07:30.373 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 14:07:30.375 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-29 14:07:30.391 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-29 14:07:30.391 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-29 14:07:30.391 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-07-29 14:07:31.310 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:07:31.326 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:07:31.356 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:07:31.865 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8090 (http)
2025-07-29 14:07:31.891 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8090"]
2025-07-29 14:07:31.892 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 14:07:31.892 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-29 14:07:32.043 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-29 14:07:32.043 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3736 ms
2025-07-29 14:07:32.712 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-29 14:07:32.713 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-29 14:07:32.714 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-29 14:07:34.609 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[*************:13337], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-07-29 14:07:34.979 [cluster-ClusterId{value='688865269ff1107be2816b00', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:20339}] to *************:13337
2025-07-29 14:07:34.979 [cluster-rtt-ClusterId{value='688865269ff1107be2816b00', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:20340}] to *************:13337
2025-07-29 14:07:34.979 [cluster-ClusterId{value='688865269ff1107be2816b00', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=130580875}
2025-07-29 14:07:37.393 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='classpath:/static'] with []
2025-07-29 14:07:37.465 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]
2025-07-29 14:07:37.485 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/hi'], Ant [pattern='/user/me'], Ant [pattern='user/detail']]
2025-07-29 14:07:37.513 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure any request
2025-07-29 14:07:39.884 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
2025-07-29 14:07:39.948 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：default
2025-07-29 14:07:39.951 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=pageSize,默认首页=1,默认页大小=10,最大页大小=-1)
2025-07-29 14:07:39.953 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
2025-07-29 14:07:40.199 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - magic-api工作目录:db://magic_api_file//magic-api
2025-07-29 14:07:40.214 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:log -> interface org.slf4j.Logger
2025-07-29 14:07:40.218 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
2025-07-29 14:07:40.219 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
2025-07-29 14:07:40.219 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
2025-07-29 14:07:40.219 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
2025-07-29 14:07:40.219 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
2025-07-29 14:07:40.219 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
2025-07-29 14:07:40.220 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：log
2025-07-29 14:07:40.225 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：db
2025-07-29 14:07:40.225 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导包：cn.hutool.core.*
2025-07-29 14:07:40.327 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8090"]
2025-07-29 14:07:40.370 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8090 (http) with context path ''
2025-07-29 14:07:41.000 [main] INFO  org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-29 14:07:41.026 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Started AuthServerApplication in 13.821 seconds (JVM running for 20.902)
2025-07-29 14:07:41.043 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-07-29 14:07:42.152 [main] INFO  com.zaxxer.hikari.pool.PoolBase - DatebookHikariCP - Driver does not support get/set network timeout for connections. (oracle.jdbc.driver.T4CConnection.getNetworkTimeout()I)
2025-07-29 14:07:42.537 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Start completed.
2025-07-29 14:07:47.326 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：datacenter
2025-07-29 14:07:52.781 [http-nio-8090-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 14:07:52.781 [http-nio-8090-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-29 14:07:52.784 [http-nio-8090-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-29 14:07:53.483 [http-nio-8090-exec-1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:20341}] to *************:13337
2025-07-29 14:07:57.945 [http-nio-8090-exec-7] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:7, serverValue:20345}] to *************:13337
2025-07-29 14:07:57.946 [http-nio-8090-exec-4] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:5, serverValue:20342}] to *************:13337
2025-07-29 14:07:57.945 [http-nio-8090-exec-3] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:4, serverValue:20344}] to *************:13337
2025-07-29 14:07:57.946 [http-nio-8090-exec-5] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:6, serverValue:20343}] to *************:13337
2025-07-29 14:07:59.059 [http-nio-8090-exec-7] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:8, serverValue:20346}] to *************:13337
2025-07-29 14:08:07.312 [http-nio-8090-exec-6] INFO  com.sanyth.auth.server.core.handler.MyAuthenticationSuccessHandler - Success hanlder
2025-07-29 14:08:09.694 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:10.935 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:18.959 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:19.040 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:19.111 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:19.111 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:19.191 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:19.248 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:20.376 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:20.503 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:24.044 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:25.244 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:26.353 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:26.475 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:26.913 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=认证应用管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.OauthClientDetails@31b08ea5, operator=10001, action=查询认证应用, fail=false, createTime=Tue Jul 29 14:08:26 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.OauthClientDetailsController})
2025-07-29 14:08:31.785 [http-nio-8090-exec-6] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:31.911 [http-nio-8090-exec-6] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:32.159 [http-nio-8090-exec-6] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=认证应用管理, subType=管理视图, bizNo={id=b9304612462d9525bfd220654aae5224}, operator=10001, action=删除认证应用, fail=false, createTime=Tue Jul 29 14:08:32 CST 2025, extra=, codeVariable={MethodName=delete, ClassName=class com.sanyth.auth.server.web.OauthClientDetailsController})
2025-07-29 14:08:33.127 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:33.254 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:08:33.511 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=认证应用管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.OauthClientDetails@3e50e2e8, operator=10001, action=查询认证应用, fail=false, createTime=Tue Jul 29 14:08:33 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.OauthClientDetailsController})
2025-07-29 14:09:35.367 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:09:35.425 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:09:35.481 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:09:35.482 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:09:35.517 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:09:35.543 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:09:35.591 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:09:35.651 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:09:35.655 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:09:35.670 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:09:35.719 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:09:38.427 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo={roleId=}, operator=10001, action=查询用户管理, fail=false, createTime=Tue Jul 29 14:09:38 CST 2025, extra=, codeVariable={MethodName=list, ClassName=class com.sanyth.auth.server.web.SytPermissionAccountController})
2025-07-29 14:10:00.224 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:10:00.394 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:10:00.854 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo={{#params}}, operator=10001, action=删除用户, fail=false, createTime=Tue Jul 29 14:10:00 CST 2025, extra=, codeVariable={MethodName=delete, ClassName=class com.sanyth.auth.server.web.SytPermissionAccountController})
2025-07-29 14:10:02.056 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:10:02.206 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:10:04.926 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo={roleId=}, operator=10001, action=查询用户管理, fail=false, createTime=Tue Jul 29 14:10:04 CST 2025, extra=, codeVariable={MethodName=list, ClassName=class com.sanyth.auth.server.web.SytPermissionAccountController})
2025-07-29 14:10:12.486 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:10:12.644 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:10:14.789 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:10:18.535 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:10:18.692 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:10:24.077 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:10:24.234 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:10:24.505 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.SytSysParam@5d17071e, operator=10001, action=查询系统参数, fail=false, createTime=Tue Jul 29 14:10:24 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.SytSysParamController})
2025-07-29 14:10:29.366 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:10:29.506 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:10:29.828 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo={"id":"0012ce8ab2bc448198ba4d451bee4bc1"}, operator=10001, action=删除系统参数, fail=false, createTime=Tue Jul 29 14:10:29 CST 2025, extra=, codeVariable={MethodName=delete, ClassName=class com.sanyth.auth.server.web.SytSysParamController})
2025-07-29 14:10:30.966 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:10:31.114 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-07-29 14:10:31.337 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.SytSysParam@29c22139, operator=10001, action=查询系统参数, fail=false, createTime=Tue Jul 29 14:10:31 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.SytSysParamController})
