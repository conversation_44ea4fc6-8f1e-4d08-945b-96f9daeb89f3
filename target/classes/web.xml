<filter>
    <filter-name>securityFilter</filter-name>
    <filter-class>com.sanyth.sso.client.validation.AuthenticationFilter</filter-class>
    <init-param>
        <param-name>clientId</param-name>
        <!-- 门户配置的clientId -->
        <param-value>70np33956d3r687O</param-value>
    </init-param>
    <init-param>
        <param-name>clientSecret</param-name>
        <!-- 门户配置的clientSecret -->
        <param-value>T7b93Fc76Co382ZP</param-value>
    </init-param>
    <init-param>
        <param-name>serverName</param-name>
        <!-- 认证服务器地址 -->
        <param-value>http://211.103.188.29:8282</param-value>
    </init-param>
    <init-param>
        <param-name>redirectUri</param-name>
        <!-- 回调地址 -->
        <param-value>http://211.103.188.29/oauth.jsp</param-value>
    </init-param>
</filter>
<filter-mapping>
    <filter-name>securityFilter</filter-name>
    <!-- 拦截的地址 -->
    <url-pattern>/oauth.jsp</url-pattern>
</filter-mapping>