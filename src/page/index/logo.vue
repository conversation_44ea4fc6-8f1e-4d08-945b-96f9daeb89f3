<template>
  <div class="avue-logo">
    <transition name="fade">
      <span v-if="keyCollapse"
            class="avue-logo_subtitle"
            key="0">
        <img class="schoolLogoFold" :src="schoolLogo" alt="">
      </span>
    </transition>
    <transition-group name="fade">
      <template v-if="!keyCollapse">
        <span class="avue-logo_title"
              key="1"><img class="schoolLogo" :src="schoolLogo" alt=""><span style="display: block;">{{website.indexTitle}} </span><span class="secondTitleLogo">Identity Authentication</span></span>
      </template>
    </transition-group>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {GetSysParam} from "@/api/sysParam";
export default {
  name: "logo",
  data() {
    return {
        schoolLogo:''
    };
  },
  created() {
      this.onLoad(this.pageParam());
  },
  computed: {
    ...mapGetters(["website", "keyCollapse"])
  },
  methods: {
      onLoad(param){
          GetSysParam(param).then(res=>{
              const data = res.data.info;
              let img = JSON.parse(data.img);
              if (img.length > 0) {
                  this.schoolLogo = img[0].url;
              }
          })
      },
      pageParam() {
          return {
              idOrNameOrType: "indexLogo"
          }
      },
  }
};
</script>

<style lang="scss">
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter-active {
  transition: opacity 2.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.avue-logo {
  // position: fixed;
  // top: 0;
  // left: 0;
  width: 240px;
  // height: 64px;
  // line-height: 64px;
  background-color: #20222a;
  font-size: 20px;
  overflow: hidden;
  box-sizing: border-box;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
  color: rgba(255, 255, 255, 0.8);
  z-index: 1024;
  &_title {
    display: block;
    text-align: center;
    font-weight: 300;
    font-size: 20px;
  }
  &_subtitle {
    display: block;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
  }
}
.schoolLogo{
	width: 55px;
	height: 55px;
	border: 2px solid #eee;
	border-radius: 50%;
  box-sizing: border-box;
  vertical-align: middle;
}
.schoolLogoFold{
	width: 35px;
	height: 35px;
	margin-left: -6px;
  vertical-align: middle;
}
.secondTitleLogo{
	font-size: 12px;
	display: block;
	color: #a7b1c2;
	text-align: center;
	line-height: 12px;
	display: block;
	margin-top: -5px;
}
</style>