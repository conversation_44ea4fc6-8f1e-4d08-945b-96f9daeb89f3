<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1,user-scalable=no,shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">
    <title>统一身份认证登录</title>

    <link rel="stylesheet" type="text/css" media="all" href="manage/css/bootstrap.min.css"/>
    <link rel="stylesheet" type="text/css" media="all" href="manage/css/signin.css"/>
    <script type="text/javascript" src="manage/js/jquery-3.4.1.min.js"></script>
    <script type="text/javascript" src="manage/js/security.js"></script>
    <script type="text/javascript" src="manage/js/login.js"></script>
    <style>
        body {
            padding: 0;
            background: #fff;
        }

        .entry-header {
            height: 90px;
            /*min-height: 80px;*/
            width: 100%;
            /*border-top: 5px solid #1670a6;*/
            box-sizing: border-box;
        }

        .entry-header p {
            line-height: 80px;
        }

        .logo-img {
            height: 66px;
            /*width: 482px;*/
            float: left;
            margin-left: 20px;
            margin-top: 10px;
        }

        .entry-header span {
            color: #666666;
            font-size: 22px;
            margin-left: 20px;
            line-height: 100px;
            padding-left: 20px;
            display: inline-block;
            height: 40px;
            line-height: 40px;
            margin-top: 25px;
        }

        .titleName{
            border-left: 1px solid #B7B8B8;
        }

        .entry-header {
            color: #1e649f;
            font-size: 20px;
        }

        .entry-main {
            width: 100%;
            position: relative;
            overflow: hidden;
            height: calc(100vh - 90px);
            /*margin-top: 35px;*/
        }

        .login_pics {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
        }

        .long-img {
            width: 100%;
            height: 100%;
        }

        .left-img{
            width: 40%;
            position: absolute;
            left: 15%;
            top: 10%;
        }

        .container {
            position: absolute;
            width: 390px;
            border-radius: 5px;
            top: 10%;
            right: 10%;
            background: rgba(255,255,255,0.95);
            min-height: 360px;
            -webkit-box-shadow:3px 3px 5px rgb(0 0 0 / 15%);
            -moz-box-shadow:3px 3px 5px rgb(0 0 0 / 15%);
            box-shadow:3px 3px 5px rgb(0 0 0 / 15%);
        }

        .form-signin {
            /*margin-top: 55px;*/
            font-size: 14px;
            padding: 20px 15px 20px;
            /*border-top: 2px solid #1e649f;*/
        }
        .phoneLogin{
            font-size: 14px;
            /*padding: 20px 15px 20px;*/
            display: none;
        }

        .form-signin>p{
            margin-bottom: 20px;
        }

        .headWrap{
            width: 100%;
            padding: 0 30px;
        }

        .form-signin-heading {
            text-align: center;
            font-size: 16px;
            margin-top: 15px !important;
            margin-bottom: 15px !important;
            font-weight: normal;

            line-height: 45px;
            width: 50%;
            float: left;
            cursor: pointer;
        }

        .headClick{
            color: #1990FF;
            font-weight: bold;
            border-bottom: 2px solid #1990FF;
        }

        .form-signin-heading img {
            margin-right: 5px;
            vertical-align: sub;
        }

        .form-signin .form-control {
            font-size: 14px;
        }

        .btn-lg {
            background: #1990FF;
            border-color: #1990FF;
            height: 42px;
            font-size: 16px;
            cursor: pointer;
        }

        .elseLogin{
            width: 100%;
            height: 40px;
            line-height: 40px;
            margin-top: -10px;
            margin-bottom: 10px;
            color: #666;
            font-size: 12px;
            padding-left: 30px;
        }

        .elseLogin img{
            width: 25px;
            height: 25px;
            margin-left: 10px;
            margin-top: -4px;
            cursor: pointer;
        }

        .entry-footer {
            height: 50px;
            width: 100%;
            padding: 0px 20px;
            text-align: center;
            line-height: 52px;
            color: #f8f8f8;
            font-size: 12px;
            position: absolute;
            left: 0;
            bottom: 0;
            background: rgba(0,0,0,0.2);
        }
        .entry-footer p{
            margin-bottom: 0;
        }
        .findWrap{
            width: 100%;
            height: 30px;
            line-height: 30px;
            font-size: 12px;
            margin-bottom: 10px;
            margin-top: -10px;
        }
        .keep{
            float: left;
            color: #999;
        }
        .keep input{
            margin-right: 5px;
            vertical-align: text-bottom;
        }
        .find{
            float: right;
            color: #1990FF;
            cursor: pointer;
        }

        .entry-header .loginHelp{
            float: right;
            margin-right: 3%;
            color: #1890ff;
            font-size: 14px;
            cursor: pointer;
        }
        .loginHelp img{
            width: 18px;
            margin-right: 5px;
        }

        .helpWrap{
            width: 500px;
            font-size: 12px;
            line-height: 22px;
            color: #666;
            padding: 20px;
            border-radius: 5px;
            position: fixed;
            top: 90px;
            right: 3%;
            z-index: 10;
            display: none;
            background: -webkit-linear-gradient(#f8f8f8, #ffffff);
            background: -o-linear-gradient(#f8f8f8, #ffffff);
            background: -moz-linear-gradient(#f8f8f8, #ffffff);
            background: linear-gradient(#f8f8f8, #ffffff);
            -webkit-box-shadow:3px 3px 5px rgb(0 0 0 / 15%);
            -moz-box-shadow:3px 3px 5px rgb(0 0 0 / 15%);
            box-shadow:3px 3px 5px rgb(0 0 0 / 15%);
        }

        .helpWrap p{
            margin: 5px 0;
        }

        .helpWrap:after{
            width: 0px;
            height: 0px;
            border-width: 12px;
            border-style: solid;
            border-color: transparent transparent #f8f8f8 transparent;
            position: absolute;
            content: ' ';
            right: 20px;
            top: -24px;
        }

        .btn-phone{
            display: none;
        }

        /*.yzmWrap input{*/
        /*    width: 160px !important;*/
        /*}*/
        /*.yzmWrap img,.yzmWrap button{*/
        /*    width: 126px !important;*/
        /*}*/

        .mobileTip{
            display: none;
        }

        .container input::-webkit-input-placeholder {
            /* WebKit browsers */
            color: #ccc;
        }
        .container input:-moz-placeholder {
            /* Mozilla Firefox 4 to 18 */
            color: #ccc;
        }
        .container input::-moz-placeholder {
            /* Mozilla Firefox 19+ */
            color: #ccc;
        }
        .container input:-ms-input-placeholder {
            /* Internet Explorer 10+ */
            color: #ccc;
        }
        .form-control{
            border-radius: 3px !important;
            height: 42px !important;
        }

        .blackBg{
            width: 100%;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 100000;
            display: none;
            background: rgba(0,0,0,0.2);
        }

        .loginTipWrap{
            width: 300px;
            height: 150px;
            padding: 20px;
            position: absolute;
            left: 50%;
            top: 40%;
            margin-left: -150px;
            margin-top: -65px;
            background: #fff;
            border-radius: 3px;
            box-shadow: 3px 3px 5px rgb(0 0 0 / 15%);
            z-index: 100001;
        }
        .loginTip{
            font-size: 12px;
            line-height: 25px;
        }

        .closeButtton{
            position: absolute;
            right: 20px;
            bottom: 20px;
            width: 60px;
            height: 30px;
            text-align: center;
            line-height: 30px;
            border-radius: 3px;
            background: #1890ff;
            color: #fff;
            font-size: 12px;
            cursor: pointer;
        }

        @media screen and (min-width: 768px) and (max-width: 1024px) {
            .left-img{
                display: none;
            }
            .logo-img{
                margin-top: 10px;
            }
            .btn-phone{
                display: block;
            }
        }
        @media screen and (min-width: 320px)and (max-width: 750px){
            .left-img{
                display: none;
            }
            .logo-img{
                /*width: 40%;*/
                height: auto;
                margin-left: 5%;
                margin-top: 20px;
                height: 50px;
            }
            .titleName{
                display: none;
            }
            .entry-header span{
                font-size: 16px;
            }
            .container{
                width: 90%;
                right: 5%;
            }
            .btn-phone{
                display: block;
            }
            .elseLogin{
                padding-left: 15px;
            }
            .entry-header .loginHelp{
                position: fixed;
                top: 40px;
                right: 0px;
                margin-right: 0;
                z-index: 10;
                background: #eee;
                text-align: center;
                border-radius: 3px 0 0 3px;
                padding: 0 10px;
                display: none;
            }
            .helpWrap{
                width: 90%;
                top: 160px;
                right: 5%;
            }
            .helpWrap:after{
                right: 8px;
            }
            .entry-header .mobileTip{
                display: block;
                position: fixed;
                top: 75px;
                right: 20px;
                background: rgba(0,0,0,0.5);
                width: 40px;
                height: 40px;
                border-radius: 50%;
                padding-left: 0;
            }
            .entry-header .mobileTip img{
                width: 30px;
                margin-top: 2px;
                margin-left: 5px;
            }
        }
        @media screen and (max-width: 320px){
            .yzmWrap button{
                font-size: 12px;
                padding: 0;
            }
            .container{
                width: 90%;
                right: 5%;
                top: 4%;
            }
            .btn-phone{
                display: block;
            }
            .elseLogin{
                padding-left: 15px;
            }
            .elseLogin img{
                margin-left: 5px;
            }
            .entry-header .loginHelp{
                position: fixed;
                top: 40px;
                right: 0px;
                margin-right: 0;
                z-index: 10;
                background: #eee;
                text-align: center;
                border-radius: 3px 0 0 3px;
                padding: 0 10px;
                display: none;
            }
            .helpWrap{
                width: 90%;
                top: 140px;
                right: 5%;
            }
            .helpWrap:after{
                right: 8px;
            }
            .entry-header .mobileTip{
                display: block;
                position: fixed;
                top: 55px;
                right: 20px;
                background: rgba(0,0,0,0.5);
                width: 40px;
                height: 40px;
                border-radius: 50%;
                padding-left: 0;
            }
            .entry-header .mobileTip img{
                width: 30px;
                margin-top: 2px;
                margin-left: 5px;
            }
        }

    </style>
</head>
<body>
<div class="wrap">
    <div class="entry-header">
        <img class="logo-img" th:src="${loginLogo}">
        <span class="titleName">统一身份认证平台</span>
        <span class="loginHelp"><img th:src="@{/manage/img/tip.png}" alt="">登录说明</span>
        <span class="loginHelp mobileTip"><img th:src="@{/manage/img/mobileTip.png}" alt=""></span>
    </div>
    <div class="helpWrap">
        <h6>登录说明</h6>
        <p th:utext="${loginHelp}">1、统一身份认证是智慧校园的一个基础服务，只需输入一次用户名和密码，便可登录接入本平台的所有应用。<br/>
            2. 统一身份认证用户名为本人学号或者教工号，初始密码为本人身份证号后六位，如果身份证号以X结尾的，则X为大写。<br/>
            3. 第一次登录成功后请及时修改密码并绑定本人的邮箱或手机，以便忘记密码后自助找回。<br/>
            4. 如有登录方面其他问题，请与网络信息中心联系。<br/>
            5. 为保障系统的稳定性，建议使用IE10及以上浏览器。</p>
        <button class="btn btn-lg btn-primary btn-block btn-phone" style="margin-top: 10px;">关闭</button>
    </div>
    <div class="entry-main">
        <div id="loginbj" class="login_pics">
            <img class="long-img" th:src="${loginBackground1}" alt="">
        </div>
        <img class="left-img" th:src="${loginBackground2}" alt="">
        <div class="container">
            <div class="headWrap">
                <h2 class="form-signin-heading numberTitle headClick">账号登录</h2>
                <h2 class="form-signin-heading phoneTitle">手机登录</h2>
            </div>

            <form class="form-signin numberLogin" method="post" autocomplete="off" th:action="@{/login}" >
                <input type="hidden" id="errorMsg" th:if="${param.error}"
                       th:value="${session.SPRING_SECURITY_LAST_EXCEPTION.message}">
<!--                <h2 class="form-signin-heading"><img th:src="@{/img/newzh.png}" alt="">用户登录</h2>-->

                <p>
                    <label for="username" class="sr-only">Username</label>
                    <input type="text" id="user" class="form-control" placeholder="请输入账号/手机号/邮箱/证件号" required
                           autofocus>
                    <input type="hidden" id="username" name="username">
                    <input type="hidden" id="password" name="password">
                    <input type="hidden" id="vercode" name="vercode">
                </p>
                <p>
                    <label for="password" class="sr-only">Password</label>
                    <input type="password" id="pass" class="form-control" placeholder="请输入密码" required>
                </p>
                <p class="yzmWrap loginYzm" style="display: none;">
                    <label for="username" class="sr-only">Captcha</label>
                    <input type="text" id="captcha"
                           style="width:50%;vertical-align: middle;height: 42px;border: 1px solid rgba(0,0,0,.15);border-radius: 3px;padding-left: 10px;"
                           placeholder="请输入验证码" required autofocus>
                    <img th:src="@{/captcha}" title="换一张" id="captchaImg" onclick="changeCaptcha()" style="width:48%;height:42px;vertical-align:middle; cursor:pointer;">
                </p>
                <div class="findWrap">
                    <span class="keep" title="非本人电脑不建议采取此功能"><input id="remember" type="checkbox">保存密码</span>
                    <a th:href="@{/findPassword}"><span class="find">找回密码</span></a>
                </div>
                <button class="btn btn-lg btn-primary btn-block" type="button" id="userlogin">登录</button>
<!--                <div id="loading" style="text-align: center;display: none"><img th:src="@{/img/loading.gif}"></div>-->

            </form>
            <form class="form-signin phoneLogin">
                <p>
                    <input type="text" id="phone" class="form-control" placeholder="请输入手机号" required
                           autofocus>
                </p>
<!--                <p class="yzmWrap">-->
<!--                    <input type="text" id="phone-captcha"-->
<!--                           style="vertical-align: middle; width: 165px;height: 42px;border: 1px solid rgba(0,0,0,.15);border-radius: 3px;padding-left: 10px;"-->
<!--                           placeholder="请输入验证码" required autofocus>-->
<!--                    <img th:src="@{/captcha}" title="换一张" id="phone-captchaImg" onclick="changeCaptcha()" style="vertical-align:middle; cursor:pointer;">-->
<!--                </p>-->
                <p class="yzmWrap">
                    <input type="text" id="phone-yzm" class="form-control" style="width:50%;height: 42px;display: inline-block;border-radius: 3px;" placeholder="请输入短信验证码" required>
                    <button class="btn btn-lg btn-primary yzmPhone" style="width:calc(50% - 14px);background: #fff;color: #666;border: 1px solid rgba(0,0,0,0.15);height: 42px;font-size: 14px;margin-top: -3px;margin-left: 10px;border-radius: 3px;">获取验证码</button>
                </p>
                <div class="one clearfix">
                    <p id="redTip" style="text-align:center;color:red;font-size:12px;display: none;">查询不到账号信息，请查证后重新输入</p>
                    <p id="greyTip" style="text-align:center;color:#999;font-size:12px;display: none;">验证码已发送到12******************的手机号中</p>
                </div>
                <button class="btn btn-lg btn-primary btn-block" type="button" id="phone-userlogin">登录</button>
            </form>
            <div class="elseLogin">
                <span>其他登录方式</span>
                <!--<a href="/user/auth/login/dingtalk"><img title="钉钉" th:src="@{/img/ding.png}"></a>-->
<!--                <a href="/user/auth/login/wechat"><img title="微信" th:src="@{/img/weixin.png}"></a>-->
                <a href="/user/auth/login/wechat_enterprise"><img title="企业微信" th:src="@{/manage/img/qywx.png}"></a>
<!--                <a href="/user/auth/login/qq"><img title="QQ" th:src="@{/img/qq.png}"></a>-->
<!--                <a href="/user/auth/login/yiban"><img title="易班" th:src="@{/img/yiban.png}"></a>-->
<!--                <a href="/user/auth/login/weibo"><img title="微博" th:src="@{/img/weibo.png}"></a>-->
            </div>
        </div>
        <div class="entry-footer">
            <p th:utext="${dbxx}"></p>
        </div>
    </div>

</div>
<div class="blackBg">
    <div class="loginTipWrap">
        <p class="loginTip">系统检测到你现在还未绑定账号，请输入统一身份账号和密码，登录成功后系统会自动为你完成绑定。</p>
        <div class="closeButtton">确定</div>
    </div>
</div>

</body>
</html>
<script>
    $(function(){
        $(".numberTitle").on("click",function (){
            $(this).addClass("headClick").siblings().removeClass("headClick");
            $(".numberLogin").css("display","block")
            $(".phoneLogin").css("display","none")
        })
        $(".phoneTitle").on("click",function (){
            $(this).addClass("headClick").siblings().removeClass("headClick");
            $(".numberLogin").css("display","none")
            $(".phoneLogin").css("display","block")
        })
        $(".loginHelp").on("mouseover",function (){
            $(".helpWrap").fadeIn(300);
        })
        $(".loginHelp").on("mouseleave",function (){
            $(".helpWrap").fadeOut(300);
        })


        $("#phone").on("focus", function(e) {
            $("#redTip").hide();
            $("#greyTip").hide();
        });
        $("#phone-yzm").on("focus", function(e) {
            $("#redTip").hide();
            $("#greyTip").hide();
        });
        document.querySelector('link[rel="icon"]').href = [[${loginLogo}]]
    })
    // 将function函数赋值给onload对象
    window.onload = function ()
    {
        //从Cookie获取到用户名
        var username = getCookie("username") ;
        //如果用户名为空,则给表单元素赋空值
        if ( username == "" )
        {
            document.getElementById("user").value="" ;
            document.getElementById("pass").value="" ;
            document.getElementById("remember").checked=false ;
        }
        //获取对应的密码,并把用户名,密码赋值给表单
        else
        {
            var password = getCookie("password") ;
            document.getElementById("user").value = username ;
            document.getElementById("pass").value = password ;
            document.getElementById("remember").checked = true ;
        }

        // 提示信息
        var loginTips = getCookie("loginTips");
        if(!!loginTips){
            // alert(loginTips);
            $(".blackBg").fadeIn(300);
        }
        $(".closeButtton").on("click",function (){
            $(".blackBg").fadeOut(300);
            delCookie("loginTips");
        })
    }
</script>