<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1,user-scalable=no,shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">
    <title>统一身份认证登录</title>

    <link rel="stylesheet" type="text/css" media="all" href="/manage/css/bootstrap.min.css"/>
    <link rel="stylesheet" type="text/css" media="all" href="/manage/css/signin.css"/>
    <link rel="icon" href="favicon.ico" />
    <script type="text/javascript" src="/manage/js/jquery-3.4.1.min.js"></script>
    <script type="text/javascript" src="/manage/js/security.js"></script>
    <script type="text/javascript" charset="UTF-8" src="/manage/js/wwLogin-1.0.0.js"></script>
    <script type="text/javascript" src="/manage/js/login.js"></script>
    <style>
        html {
            height: 100%;
            width: 100%;
            padding: 0;
        }
        body {
            height: 100%;
            padding: 0;
            margin: 0;
            background: #fff;
        }


        .btn-group-lg>.btn, .btn-lg {
            padding: .6rem 1rem;
            font-size: 1.25rem;
            line-height: 1.5;
            border-radius: 2px;
            height: 32px;}
        .phoneLogin{
            font-size: 14px;
            display: none;
        }
        .qywxLogin{
            font-size: 14px;
            display: none;
        }
        .headWrap{
            width: 100%;
        }

        .form-signin-heading {
            text-align: center;
            font-size: 16px;
            margin-top: 15px !important;
            margin-bottom: 15px !important;
            font-weight: normal;
            color: #fff;
            line-height: 45px;
            width: 50%;
            float: left;
            cursor: pointer;
        }

        .headClick{
            color: #ffffff;
            font-weight: bold;
            border-bottom: 1px solid #ffffff50;
        }

        .form-signin-heading img {
            margin-right: 5px;
            vertical-align: sub;
        }

        .form-signin .form-control {
            font-size: 14px;
            color: #fff;
            margin-top: 5px;
            background-color: rgb(10 10 10 / 23%);
            border: 0px;
        }

        .btn-lg {
            line-height: 0.5;
            background: #1990FF;
            border-color: #1990FF;
            height: 35px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            color: #fff;
        }

        .elseLogin{
            width: 100%;
            height: 40px;
            line-height: 40px;
            margin-top: 10px;
            margin-bottom: 10px;
            color: #ffffffe0;
            padding-left: 15px;
            font-size: 12px;
        }
        .elseLogin img{
            width: 25px;
            height: 25px;
            margin-left: 10px;
            margin-top: -4px;
            cursor: pointer;
        }
        .findWrap{
            width: 100%;
            height: 30px;
            line-height: 30px;
            font-size: 12px;
            margin-bottom: 10px;
        }
        .keep{
            float: left;
            color: #ffffffe0;
        }
        .keep input{
            margin-right: 5px;
            vertical-align: text-bottom;
        }
        .find{
            float: right;
            color: #ffffffe0;
            cursor: pointer;
        }

        .loginHelp img{
            width: 18px;
            margin-right: 5px;
        }

        .helpWrap{
            width: 500px;
            font-size: 12px;
            line-height: 22px;
            color: #666;
            padding: 20px;
            border-radius: 5px;
            position: absolute;
            top: -130px;
            right: 60px;
            z-index: 10;
            display: none;
            background: -webkit-linear-gradient(#f8f8f8, #ffffff);
            background: -o-linear-gradient(#f8f8f8, #ffffff);
            background: -moz-linear-gradient(#f8f8f8, #ffffff);
            background: linear-gradient(#f8f8f8, #ffffff);
            -webkit-box-shadow:3px 3px 5px rgb(0 0 0 / 15%);
            -moz-box-shadow:3px 3px 5px rgb(0 0 0 / 15%);
            box-shadow:3px 3px 5px rgb(0 0 0 / 15%);
        }

        .helpWrap p{
            margin: 5px 0;
        }

        .helpWrap:after{
            width: 0px;
            height: 0px;
            border-width: 12px;
            border-style: solid;
            border-color: transparent transparent transparent #f8f8f8;
            position: absolute;
            content: ' ';
            right: -20px;
            top: 145px;
        }

        .btn-phone{
            display: none;
        }
        .container input::-webkit-input-placeholder {
            /* WebKit browsers */
            color: #ccc;
        }
        .container input:-moz-placeholder {
            /* Mozilla Firefox 4 to 18 */
            color: #ccc;
        }
        .container input::-moz-placeholder {
            /* Mozilla Firefox 19+ */
            color: #ccc;
        }
        .container input:-ms-input-placeholder {
            /* Internet Explorer 10+ */
            color: #ccc;
        }
        .form-control{
            width: 100%;
            border-radius: 2px !important;
            height: 35px !important;
        }


        @media screen and (min-width: 768px) and (max-width: 1024px) {
            .left-img{
                display: none;
            }
            .logo-img{
                margin-top: 10px;
            }
            .btn-phone{
                display: block;
            }
        }
        @media screen and (min-width: 320px)and (max-width: 750px){
            .left-img{
                display: none;
            }
            .logo-img{
                /*width: 40%;*/
                height: auto;
                margin-left: 5%;
                margin-top: 20px;
                height: 50px;
            }
            .container{
                width: 90%;
                right: 5%;
            }
            .btn-phone{
                display: block;
            }
            .helpWrap{
                width: 90%;
                top: 160px;
                right: 5%;
            }
            .helpWrap:after{
                right: 8px;
            }
        }
        @media screen and (max-width: 320px){
            .yzmWrap button{
                font-size: 12px;
                padding: 0;
            }
            .container{
                width: 90%;
                right: 5%;
                top: 4%;
            }
            .btn-phone{
                display: block;
            }
            .helpWrap{
                width: 90%;
                top: 140px;
                right: 5%;
            }
            .helpWrap:after{
                right: 8px;
            }
        }



        .wrap{
            width: 100%;
            height: 100%;
            overflow-y: auto;
            background: rgba(192, 192, 192, 0.69);
            position: relative;
        }
        .backimg{
            width: 100%;
            height: 100%;
            overflow-y: auto;
            vertical-align: middle;
            min-height: 620px;

        }
        .logoimg{
            height: 70px;
            position: absolute;
            top: 20px;
            left: 30px;
        }
        .content{
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            height: 100%;
            width: 25%;
            /*min-width: 450px;*/
            background-color: rgba(12, 12, 12, 0.3);
            /*overflow-y: auto;*/
            min-height: 620px;
        }
        .content_box{
            width: 75%;
            /*height: 55%;*/
            background-color: rgba(12, 12, 12, 0.42);
            margin: auto;
            padding: 20px 30px 20px;
            border-radius: 2px;
        }
        .content_bottom{
            display: flex;
            justify-content: center;
            width: 100%;
            height: 50px;
            margin-top: 25px;
        }
        .content_bottom-box{
            width: 46px;
            height: 46px;
            border-radius: 23px;
            border: 1px solid rgba(255, 255, 255, 0.62);
            margin-left: 10px;
        }
        .content_bottom-box.loginHelp {
            position: relative;
        }
        .content_bottom-box img{
            width: 26px;
            height: 26px;
            margin: 9px 9px;
            color: rgba(255, 255, 255, 0.62);
        }

        #wx-qr-container {
            display: none;
        }

        .overlay {
            display: none;
            /*position: absolute;*/
            /*top: 0;*/
            /*left: 0;*/
            width: 240px;
            height: 250px;
            /*background-color: white; !* 白板颜色 *!*/
            /*z-index: 2; !* 覆盖目标 div *!*/
            /*opacity: 1; !* 设置为完全不透明 *!*/
        }

        .overlay-container {
            color: white;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            align-items: center;
            padding: 10px;
        }

        .overlay-btn-container button {
            width: 64px;
            height: 32px;
        }
    </style>
</head>
<body class="body">
<div class="wrap">
    <img class="backimg" th:src="${loginBackground1}">
    <img class="logoimg" th:src="${loginLogo}">
    <div class="content">
        <p style="color: #fff;font-size: 23px;text-align: center;margin-top: 15vh;margin-bottom: 20px;">统一身份认证平台</p>
        <div class="content_box">
            <!-- 微信公众号 start -->
            <div id="wx-panel" class="overlay">
                <div class="overlay-container">
                    <img id="wx-qrcode-img" src="" alt="">
                    <p id="wx-qrcode-tip"></p>
                    <p id="wx-login-status"></p>
                </div>
            </div>
            <!-- 微信公众号 end -->

            <div class="headWrap">
                <h2 class="form-signin-heading numberTitle headClick">账号登录</h2>
                <h2 class="form-signin-heading phoneTitle">手机登录</h2>
            </div>

            <form class="form-signin numberLogin" method="post" autocomplete="off" th:action="@{/login}" >
                <input type="hidden" id="errorMsg" th:if="${param.error}"
                       th:value="${session.SPRING_SECURITY_LAST_EXCEPTION.message}">
                <!--                <h2 class="form-signin-heading"><img th:src="@{/img/newzh.png}" alt="">用户登录</h2>-->

                <p>
<!--                    <label for="username" class="sr-only">Username</label>-->
                    <input type="text" id="user" class="form-control" placeholder="请输入账号/手机号/邮箱/证件号" required
                           autofocus>
                    <input type="hidden" id="username" name="username">
                    <input type="hidden" id="password" name="password">
                    <input type="hidden" id="vercode" name="vercode">
                </p>
                <p>
<!--                    <label for="password" class="sr-only">Password</label>-->
                    <input type="password" id="pass" class="form-control" placeholder="请输入密码" required>
                </p>
                <p class="yzmWrap loginYzm" style="display: none;">
                    <label for="username" class="sr-only">Captcha</label>
                    <input type="text" id="captcha"
                           style="width:50%;vertical-align: middle;height: 42px;border: 1px solid rgba(0,0,0,.15);border-radius: 3px;padding-left: 10px;"
                           placeholder="请输入验证码" required autofocus>
                    <img th:src="@{/captcha}" title="换一张" id="captchaImg" onclick="changeCaptcha()" style="width:48%;height:42px;vertical-align:middle; cursor:pointer;">
                </p>
                <div class="findWrap">
                    <span class="keep" title="非本人电脑不建议采取此功能"><input id="remember" type="checkbox">保存密码</span>
                    <a th:href="@{/findPassword}"><span class="find">找回密码</span></a>
                </div>
                <button class="btn btn-lg btn-primary btn-block" type="button" id="userlogin">登录</button>
                <!--                <div id="loading" style="text-align: center;display: none"><img th:src="@{/img/loading.gif}"></div>-->

            </form>
            <form class="form-signin phoneLogin">
                <p>
                    <input type="text" id="phone" class="form-control" placeholder="请输入手机号" required
                           autofocus>
                </p>
                <!--                <p class="yzmWrap">-->
                <!--                    <input type="text" id="phone-captcha"-->
                <!--                           style="vertical-align: middle; width: 165px;height: 42px;border: 1px solid rgba(0,0,0,.15);border-radius: 3px;padding-left: 10px;"-->
                <!--                           placeholder="请输入验证码" required autofocus>-->
                <!--                    <img th:src="@{/captcha}" title="换一张" id="phone-captchaImg" onclick="changeCaptcha()" style="vertical-align:middle; cursor:pointer;">-->
                <!--                </p>-->
                <p class="yzmWrap">
                    <input type="text" id="phone-yzm" class="form-control" style="width:50%;height: 42px;display: inline-block;border-radius: 3px;" placeholder="请输入短信验证码" required>
                    <button class="btn btn-lg btn-primary yzmPhone" style="width:calc(50% - 14px);background: rgba(119, 108, 108, 0.68);color: #ffffff4d;border: 1px solid rgba(0,0,0,0.15);height: 30px;font-size: 14px;margin-top: -7px;margin-left: 10px;border-radius: 3px;">获取验证码</button>
                </p>
                <div class="one clearfix">
                    <p id="redTip" style="text-align:center;color:red;font-size:12px;display: none;">查询不到账号信息，请查证后重新输入</p>
                    <p id="greyTip" style="text-align:center;color:#999;font-size:12px;display: none;">验证码已发送到12******************的手机号中</p>
                </div>
                <button class="btn btn-lg btn-primary btn-block" type="button" id="phone-userlogin">登录</button>
            </form>
            <form id="js-layout-login-login-wechat-qrcode" class="form-signin qywxLogin" style="text-align:center"></form>
            <div class="elseLogin" th:if="${callbackUrl}">
                <span>扫码登录</span>
                <!--<a href="/user/auth/login/dingtalk"><img title="钉钉" th:src="@{/img/ding.png}"></a>-->
                <!--                <a href="/user/auth/login/wechat"><img title="微信" th:src="@{/img/weixin.png}"></a>-->
<!--                <a href="/user/auth/login/wechat_enterprise"><img class="qywxLoginImg" title="企业微信" th:src="@{manage/img/qywx.png}"></a>-->
                <img class="qywxLoginImg" title="企业微信" th:src="@{/manage/img/qywx.png}">
                <!--                <a href="/user/auth/login/qq"><img title="QQ" th:src="@{/img/qq.png}"></a>-->
                <!--                <a href="/user/auth/login/yiban"><img title="易班" th:src="@{/img/yiban.png}"></a>-->
                <!--                <a href="/user/auth/login/weibo"><img title="微博" th:src="@{/img/weibo.png}"></a>-->
                <p style="text-align: center;margin-top: -10px;">上网信息设备严禁处理涉密信息！</p>
            </div>
            <div class="elseLogin" id="wx-qr-container">
                <span>扫码登录</span>
                <img id="wx-qr-btn" onclick="togglePanel()" title="微信" th:src="@{/manage/platform/wei_xin/web_auth/img/logo.png}">
            </div>
        </div>
        <div class="content_bottom">

            <div class="content_bottom-box loginHelp" title="登录帮助">
                <img th:src="@{/manage/img/帮助.svg}">
                <div class="helpWrap">
                    <h6>登录说明</h6>
                    <p th:utext="${loginHelp}">1、统一身份认证是智慧校园的一个基础服务，只需输入一次用户名和密码，便可登录接入本平台的所有应用。<br/>
                        2. 统一身份认证用户名为本人学号或者教工号，初始密码为本人身份证号后六位，如果身份证号以X结尾的，则X为大写。<br/>
                        3. 第一次登录成功后请及时修改密码并绑定本人的邮箱或手机，以便忘记密码后自助找回。<br/>
                        4. 如有登录方面其他问题，请与网络信息中心联系。<br/>
                        5. 为保障系统的稳定性，建议使用IE10及以上浏览器。</p>
                    <button class="btn btn-lg btn-primary btn-block btn-phone" style="margin-top: 10px;">关闭</button>
                </div>
            </div>
            <div class="content_bottom-box" th:each="a:${promptButton}" th:title="${a.getName()}">
                <a th:href="${a.getValue()}" target="_blank"><img th:src="${a.getImg()}"></a>
            </div>
        </div>
        <p style="font-size: 12px;color: #fff;width: 70%;margin: 20px auto;text-align: center;position: absolute;bottom: 0;right: 14%;" th:utext="${dbxx}"></p>
    </div>
</div>

</body>
</html>
<script th:inline="javascript">
    $(function(){
        if( $(".wrap").width() > 800 ){
            $(".content").css("width","25%")
        }else {
            $(".content").css("width","100%")
        }
        // console.log($(".content").width())
        $(".numberTitle").on("click",function (event){
            $(this).addClass("headClick").siblings().removeClass("headClick");
            $(".numberLogin").css("display","block")
            $(".phoneLogin").css("display","none")
        })
        $(".phoneTitle").on("click",function (event){
            $(this).addClass("headClick").siblings().removeClass("headClick");
            $(".numberLogin").css("display","none")
            $(".phoneLogin").css("display","block")
        })
        $(".loginHelp").on("mouseover",function (){
            $(".helpWrap").fadeIn(300);
        })
        $(".loginHelp").on("mouseleave",function (){
            $(".helpWrap").fadeOut(300);
        })
        const defaultWechatConfig = {
            // 企业微信配置
            agentId: [[${agentId}]],//企业微信内自行获取
            appId: [[${appId}]],//企业微信内自行获取
            callbackUrl: encodeURIComponent([[${callbackUrl}]]),//企业微信内自行配置,一定要和授权完成回调域名保持一致
            // state: [[${state}]]//企业微信内自行配置获取,
            // state: "31add3a2ff3ce2eb29d0550c1e68478b"//企业微信内自行配置获取,
        };
        var w = defaultWechatConfig;
        $(".qywxLoginImg").on("click",function (event){
            if ($(".qywxLogin").css("display")==="block") {
                $(".numberTitle").addClass("headClick").siblings().removeClass("headClick");
                $(".numberLogin").css("display","block")
                $(".headWrap").css("display","block")
                $(".phoneLogin").css("display","none")
                $(".qywxLogin").css("display","none")
            }else {
                $(".headWrap").css("display","none")
                $(".numberLogin").css("display","none")
                $(".phoneLogin").css("display","none")
                $(".qywxLogin").css("display","block")
                window.WwLogin({
                    id: "js-layout-login-login-wechat-qrcode",
                    appid: w.appId,
                    agentid: w.agentId,
                    redirect_uri: w.callbackUrl,
                    state: w.state,
                    href:"data:text/css;base64,LmltcG93ZXJCb3ggLnFyY29kZSB7d2lkdGg6IDIwMHB4O30NCi5pbXBvd2VyQm94IC5pbmZvIHt3aWR0aDogMjAwcHg7fQ0KLnN0YXR1c19pY29uIHtkaXNwbGF5OiBub25lICAhaW1wb3J0YW50fQ0KLmltcG93ZXJCb3ggLnN0YXR1cyB7dGV4dC1hbGlnbjogY2VudGVyO30="
                });
            }
        })
        $(document).click(function(event){
            var _con = $('.qywxLoginImg');  // 设置目标区域
            if(!_con.is(event.target) && _con.has(event.target).length === 0&& $(".qywxLogin").css("display")==="block"){ // Mark 1
                $(".numberTitle").addClass("headClick").siblings().removeClass("headClick");
                $(".numberLogin").css("display","block")
                $(".headWrap").css("display","block")
                $(".phoneLogin").css("display","none")
                $(".qywxLogin").css("display","none")
            }
        })



        $("#phone").on("focus", function(e) {
            $("#redTip").hide();
            $("#greyTip").hide();
        });
        $("#phone-yzm").on("focus", function(e) {
            $("#redTip").hide();
            $("#greyTip").hide();
        });
        document.querySelector('link[rel="icon"]').href = [[${titlelogo}]]

    })
    // 将function函数赋值给onload对象
    window.onload = function ()
    {
        //从Cookie获取到用户名
        var username = getCookie("username") ;
        //如果用户名为空,则给表单元素赋空值
        if ( username == "" )
        {
            document.getElementById("user").value="" ;
            document.getElementById("pass").value="" ;
            document.getElementById("remember").checked=false ;
        }
        //获取对应的密码,并把用户名,密码赋值给表单
        else
        {
            var password = getCookie("password") ;
            document.getElementById("user").value = username ;
            document.getElementById("pass").value = password ;
            document.getElementById("remember").checked = true ;
        }

        // 提示信息
        var loginTips = getCookie("loginTips");
        if(!!loginTips){
            // alert(loginTips);
            $(".loginTip span").html(loginTips);
            $(".blackBg").fadeIn(300);
        }
        $(".closeButtton").on("click",function (){
            $(".blackBg").fadeOut(300);
            delCookie("loginTips");
        })
    }
</script>
<script src="/manage/js/jquery-3.4.1.min.js"></script>
<script>
    function wxLoginInit() {
        $.post("noAuth/platform/wei_xin/web_auth/rest/qrCodeEnable")
            .done(function (res) {
                if (res) {
                    $("#wx-qr-container").show();
                }
            });
    }

    $(".content_box").click(function (event) {
        // console.log(event, panel.is(event.target), btn.is(event.target));
        // 忽略二维码区域
        if ($('#wx-panel').is(event.target)) {
            return
        }
        // 忽略按钮区域
        if ($('#wx-qr-btn').is(event.target)) {
            return;
        }
        // 忽略原登录方式
        if ($(".headWrap").is(event.target)) {
            return
        }
        // 忽略“账号登录”
        if ($(".numberLogin").is(event.target)) {
            return
        }
        // 忽略“手机号登录”
        if ($(".phoneLogin").is(event.target)) {
            return
        }
        // 忽略“企业微信登录”
        if ($(".qywxLogin").is(event.target)) {
            return
        }
        closePanel();
    })

    function togglePanel() {
        let panel = document.querySelector('#wx-panel');
        if (panel.style.display === 'block') {
            closePanel();
        } else {
            showPanel();
        }
    }

    let timeOutId = null;
    function showPanel() {
        // 隐藏输入框
        $(".headWrap").hide();
        $(".numberLogin").hide();
        $(".phoneLogin").hide();

        let panel = document.querySelector('#wx-panel');
        panel.style.display = 'block';

        $.post('/noAuth/platform/wei_xin/web_auth/rest/generate_qr_code')
            .done(function (res) {
                // console.log(res);
                let $wx = $("#wx-qrcode-img");
                $wx.attr("src", res.dataUrl);
                let $wx1 = $("#wx-qrcode-tip");
                $wx1.text(res.endDatetimeText);
                $wx.show();
                $wx1.show();
                checkLoginStatus();
            })
    }

    function closePanel() {
        // console.log("closePanel");
        clearTimeout(timeOutId);
        const panel = document.querySelector('#wx-panel');
        if (panel.style.display === 'block') {
            panel.style.display = 'none';
            $("#wx-qrcode-img").hide();
            $("#wx-qrcode-tip").hide();
            $("#wx-login-status").hide();

            // 显示输入框
            $(".numberTitle").addClass("headClick").siblings().removeClass("headClick");
            $(".numberLogin").show();
            $(".headWrap").show();
        }
    }

    function checkLoginStatus() {
        let panel = document.querySelector('#wx-panel');
        if (panel.style.display !== 'block') {
            return
        }
        $.get('/noAuth/platform/wei_xin/web_auth/rest/check_status')
            .done(function (res) {
                let $wxStatus = $('#wx-login-status');
                if (!res) {
                    $wxStatus.text('已失效');
                    return;
                }
                if (res === 'init') {
                    $wxStatus.text('未扫码');
                    timeOutId = setTimeout(checkLoginStatus, 1000);
                } else if (res === 'scan') {
                    $wxStatus.text('扫码成功');
                    timeOutId = setTimeout(checkLoginStatus, 1000);
                } else if (res === 'confirm') {
                    $wxStatus.text('认证成功');
                    $.get('/noAuth/platform/wei_xin/web_auth/rest/getRedirectUrl')
                        .done(function (res) {
                            window.location.href = res;
                        })
                }
                $wxStatus.show();
            })
    }

    wxLoginInit();
</script>