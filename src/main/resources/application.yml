server:
  #采用反向代理服务器的头部信息，支持 X-Forwarded-For 等头部
  forward-headers-strategy: native
spring:
  profiles:
    active: @active.properties@
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 2048MB
mybatis-plus:
  mapperLocations: classpath:/mybatis/mapper/*.xml
  type-aliases-package: com.sanyth.auth.server.model
  global-config:
    id-type: 0
    field-strategy: 2
    db-column-underline: true
    refresh-mapper: true
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    call-setters-on-nulls: true
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 忽略url
ignored:
  # 无需登录认证的请求
  urls:
    - /
    - /index
    - /manage/**
    - /css/**
    - /js/**
    - /img/**
    - /fonts/**
    - /cdn/**
    - /svg/**
    - /util/**
    - /static/**
    - /captcha*
    - /favicon.ico
    - /oauth/**
    - /user/**
    - /findPassword*
    - /redirectPage*
    - /file/view/**
    - /file/download/**
    - /serviceValidate*
    - /SytSysSafety/getByCode*
    - /sytSysParam/getNoLogin*
    - /sytSysParam/getListNoLogin*
    - /noAuth/**
    - /magic/web/**
    - /data/api/**
  # 无需检查权限的请求, 但需登录
  limitUrls:
    - /toClient*
#ldap开关,如果用到ldap，设置为true
ldap:
  switch: false

magic-api:
  web: /magic/web
  editor-config: classpath:./magic-editor-config.js
  resource:
    type: database              # 配置接口存储方式，这里选择存在数据库中
    table-name: magic_api_file  # 数据库中的表名
    prefix: /magic-api          # 前缀
  auto-import-module: db,log     # 自动导入的模块
  auto-import-package: cn.hutool.core.*   # 自动导包
  sql-column-case: camel        #启用驼峰命名转换
  secret-key: sanyth123!@# # 远程推送时的秘钥，未配置则不开启推送
  push-path: /magic/web/_sanyth-api-sync #远程推送的路径，默认为/_magic-api-sync
  show-sql: true #配置打印SQL
  #  throw-exception: true         # 执行出错时，异常将抛出处理
  response-code:
    success: 200                #执行成功的code值
    invalid: 400                #参数验证未通过的code值
    exception: 500              #执行出现异常的code值
  #  response: |- #配置JSON格式，格式为magic-script中的表达式
  #    {
  #      code: code,
  #      message: message,
  #      data
  #    }
  #  crud: # CRUD相关配置
  #    logic-delete-column: deleted #逻辑删除列
  #    logic-delete-value: 2       #逻辑删除值
  page:
    size: pageSize              # 页大小的请求参数名称
    page: page                  # 页码的请求参数名称
    default-page: 1             # 未传页码时的默认首页
    default-size: 10            # 未传页大小时的默认页大小
  backup: #备份相关配置
    enable: false #是否启用
    max-history: -1 #备份保留天数，-1为永久保留
    #    datasource: magic  #指定数据源（单数据源时无需配置，多数据源时默认使用主数据源，如果存在其他数据源中需要指定。）
    table-name: magic_api_backup #使用数据库存储备份时的表名
  security: # 安全配置
    username: admin # 登录用的用户名
    password: sanyth123456 # 登录用的密码
