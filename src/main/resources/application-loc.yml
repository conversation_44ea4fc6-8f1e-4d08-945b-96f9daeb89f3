server:
  port: 8090
  servlet:
    context-path: /
spring:
  datasource:
    driver-class-name: oracle.jdbc.driver.OracleDriver
    url: ********************************************
    username: syt_new_portal
    password: syt_new_portal_2025
#    url: ***************************************
#    username: syt_portal_new
#    password: syt_portal_bddl
#    url: ****************************************
#    username: syt_mh
#    password: syt2022_mh
#    url: ***************************************
#    username: syt_portal
#    password: md2020_portal
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 60000
      pool-name: DatebookHikariCP
      max-lifetime: 60000
      connection-timeout: 60000
      connection-test-query: SELECT * FROM DUAL
      validation-timeout: 3000
      login-timeout: 5
  thymeleaf:
    #    prefix: file:/Users/<USER>/WorkSpace/git-sanyth-sso-server/src/main/resources
    cache: false
    mode: HTML5
    encoding: utf-8
    suffix: .html
  session:
    store-type: redis
    redis:
#      namespace: spring:session:session
    timeout: 1800
  redis:
    host: *************
    database: 8
    port: 6380
    password: 123456
    timeout: 10000ms
    connect-timeout: 10000ms
  data:
    mongodb:
      uri: **************************************************************
  resources:
    static-locations: classpath:/static
  mvc:
    static-path-pattern: /**
  ldap:
    urls: ldap://1192.168.9.136:389
    base: dc=sanyth,dc=cn
    username: cn=admin,dc=sanyth,dc=cn
    password: 123456

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

log:
  level: info
  path: logs/
  maxHistory: 1