package com.sanyth.auth.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.auth.server.dto.StatisticsDto;
import com.sanyth.auth.server.mapper.IndexDataMapper;
import com.sanyth.auth.server.service.IndexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class IndexServiceImpl extends ServiceImpl<IndexDataMapper, StatisticsDto> implements IndexService {

    @Autowired
    IndexDataMapper indexDataMapper;

    @Override
    public Map<String, Object> queryIndexData() {
        HashMap<String, Object> map = new HashMap<>();
        HashMap<String, Object> infoMap = new HashMap<>();
        infoMap.putAll(indexDataMapper.queryAccountNumber());
        List<Map<String, BigDecimal>> maps = indexDataMapper.queryNumberByField("VALIDFLAG");
        BigDecimal decimal = new BigDecimal(0);
        for (Map<String, BigDecimal> longMap : maps) {
            if (longMap.get("NAME").compareTo(decimal) == 0) {
                infoMap.put("ZXRS", longMap.getOrDefault("VALUE", decimal));
            } else if (longMap.get("NAME").compareTo(new BigDecimal(1)) == 0) {
                infoMap.put("LXRS", longMap.getOrDefault("VALUE", decimal));
            }
        }
        infoMap.putAll(indexDataMapper.queryVisitNumber());
        infoMap.putAll(indexDataMapper.queryAppNumber());
        infoMap.putAll(indexDataMapper.queryOrgNumber());
        map.put("sjgl", infoMap);
        map.put("yfw", indexDataMapper.queryVisitNumberByDay());
        map.put("rylb", indexDataMapper.queryNumberByField("EMPLOYEETYPE"));
        return map;
    }
}
