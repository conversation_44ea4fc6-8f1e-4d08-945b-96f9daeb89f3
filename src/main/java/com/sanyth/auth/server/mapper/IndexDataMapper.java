package com.sanyth.auth.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanyth.auth.server.dto.StatisticsDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
public interface IndexDataMapper extends BaseMapper<StatisticsDto> {

    Map<String, Long> queryAccountNumber();
    List<Map<String, BigDecimal>> queryNumberByField(@Param("field") String field);
    Map<String, Long> queryVisitNumber();
    Map<String, Long> queryAppNumber();
    Map<String, Long> queryOrgNumber();
    List<Map<String, BigDecimal>> queryVisitNumberByDay();


}
